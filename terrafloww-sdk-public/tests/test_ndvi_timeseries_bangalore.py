# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
NDVI Time Series Analysis for Bangalore AOI - 2024 Full Year

This test demonstrates the SDK's capability for temporal geospatial analysis
by creating a 1-year NDVI time series for Bangalore, India.
"""

import os
import sys
from datetime import datetime
from typing import List, Tuple
import pandas as pd
import pyarrow as pa
from shapely.geometry import Polygon

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import terrafloww as tfw


def load_bangalore_year_data() -> pa.Table:
    """
    Load full year 2024 data for Bangalore AOI and apply NDVI calculation.
    
    Returns:
        PyArrow Table with NDVI results for all available dates
    """
    print("🌍 Loading Bangalore 2024 full year data...")
    
    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01), 
        (77.58, 13.08), (77.55, 13.08), 
        (77.55, 13.01)
    ])
    
    print(f"📍 AOI bounds: {aoi_polygon.bounds}")
    print(f"📅 Date range: 2024-01-01 to 2024-12-31")
    
    # Load data for Q1 2024 first (testing with smaller dataset)
    workflow = tfw.load(
        "sentinel-2-l2a",
        bbox=list(aoi_polygon.bounds),
        datetime="2024-01-01/2024-03-31",  # Q1 2024 for testing
        bands=["red", "nir"]
    ).apply("ndvi", {
        "red_band": "red",
        "nir_band": "nir"
    })
    
    print("🚀 Computing NDVI for Q1 2024...")
    print("⏳ This should process ~18 scenes...")
    
    # Compute results (no head limit - get all available data)
    results = workflow.compute()
    
    print(f"✅ Computation complete!")
    print(f"📊 Total spatial windows: {len(results)}")
    print(f"📋 Columns: {results.column_names}")
    
    return results


def process_temporal_ndvi(results: pa.Table) -> pd.DataFrame:
    """
    Process NDVI results to create temporal aggregation by date.
    
    Args:
        results: PyArrow table with NDVI results
        
    Returns:
        Pandas DataFrame with date and average NDVI per date
    """
    print("📊 Processing temporal NDVI aggregation...")
    
    # Convert to pandas for easier processing
    df = results.to_pandas()
    
    print(f"📏 Raw data shape: {df.shape}")
    print(f"📅 Date range in data: {df['datetime'].min()} to {df['datetime'].max()}")
    
    # Extract date from datetime (remove time component)
    df['date'] = pd.to_datetime(df['datetime']).dt.date
    
    # Check if we have NDVI column
    if 'ndvi' not in df.columns:
        print("❌ No NDVI column found in results")
        print(f"Available columns: {df.columns.tolist()}")
        return pd.DataFrame()
    
    # Group by date and calculate statistics
    daily_stats = df.groupby('date').agg({
        'ndvi': ['mean', 'std', 'count'],
        'chunk_id': 'count'  # Number of spatial windows per date
    }).round(4)
    
    # Flatten column names
    daily_stats.columns = ['ndvi_mean', 'ndvi_std', 'ndvi_count', 'spatial_windows']
    daily_stats = daily_stats.reset_index()
    
    # Convert date back to datetime for plotting
    daily_stats['datetime'] = pd.to_datetime(daily_stats['date'])
    
    print(f"📈 Temporal aggregation complete:")
    print(f"  📅 Unique dates: {len(daily_stats)}")
    print(f"  📊 NDVI range: {daily_stats['ndvi_mean'].min():.3f} to {daily_stats['ndvi_mean'].max():.3f}")
    print(f"  🔢 Avg spatial windows per date: {daily_stats['spatial_windows'].mean():.1f}")
    
    return daily_stats


def analyze_temporal_patterns(daily_stats: pd.DataFrame) -> None:
    """
    Analyze temporal patterns in the NDVI time series.
    
    Args:
        daily_stats: DataFrame with daily NDVI statistics
    """
    print("🔍 Analyzing temporal patterns...")
    
    if daily_stats.empty:
        print("❌ No data to analyze")
        return
    
    # Monthly aggregation
    daily_stats['month'] = daily_stats['datetime'].dt.month
    monthly_stats = daily_stats.groupby('month').agg({
        'ndvi_mean': ['mean', 'std'],
        'spatial_windows': 'sum'
    }).round(4)
    
    print("\n📅 Monthly NDVI Summary:")
    print("Month | Avg NDVI | Std Dev | Total Windows")
    print("-" * 45)
    
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    
    for month in range(1, 13):
        if month in monthly_stats.index:
            row = monthly_stats.loc[month]
            ndvi_mean = row[('ndvi_mean', 'mean')]
            ndvi_std = row[('ndvi_mean', 'std')]
            windows = row[('spatial_windows', 'sum')]
            print(f"{month_names[month-1]:>5} | {ndvi_mean:>8.3f} | {ndvi_std:>7.3f} | {windows:>12.0f}")
        else:
            print(f"{month_names[month-1]:>5} | {'N/A':>8} | {'N/A':>7} | {'N/A':>12}")
    
    # Seasonal analysis
    print(f"\n🌱 Seasonal Analysis:")
    print(f"  🌸 Spring (Mar-May): {daily_stats[daily_stats['month'].isin([3,4,5])]['ndvi_mean'].mean():.3f}")
    print(f"  ☀️ Summer (Jun-Aug): {daily_stats[daily_stats['month'].isin([6,7,8])]['ndvi_mean'].mean():.3f}")
    print(f"  🍂 Autumn (Sep-Nov): {daily_stats[daily_stats['month'].isin([9,10,11])]['ndvi_mean'].mean():.3f}")
    print(f"  ❄️ Winter (Dec-Feb): {daily_stats[daily_stats['month'].isin([12,1,2])]['ndvi_mean'].mean():.3f}")


def test_ndvi_timeseries_bangalore():
    """
    Main test function for Bangalore NDVI time series analysis.
    """
    print("🧪 NDVI Time Series Analysis - Bangalore 2024")
    print("=" * 60)
    
    try:
        # Step 1: Load and compute data
        print("\n📋 Step 1: Data Loading and NDVI Computation")
        results = load_bangalore_year_data()
        
        if len(results) == 0:
            print("❌ No data returned from computation")
            return False
        
        # Step 2: Process temporal aggregation
        print("\n📋 Step 2: Temporal Processing")
        daily_stats = process_temporal_ndvi(results)
        
        if daily_stats.empty:
            print("❌ Temporal processing failed")
            return False
        
        # Step 3: Analyze patterns
        print("\n📋 Step 3: Pattern Analysis")
        analyze_temporal_patterns(daily_stats)
        
        # Step 4: Save results for visualization
        output_file = "bangalore_ndvi_2024.csv"
        daily_stats.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        print("\n🎉 NDVI Time Series Analysis Complete!")
        print("Ready for visualization step...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in NDVI time series analysis: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_ndvi_timeseries_bangalore()
    sys.exit(0 if success else 1)
