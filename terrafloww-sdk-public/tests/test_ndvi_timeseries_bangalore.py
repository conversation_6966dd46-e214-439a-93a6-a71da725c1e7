# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
NDVI Time Series Analysis for Bangalore AOI - 2024 Full Year

This test demonstrates the SDK's capability for temporal geospatial analysis
by creating a 1-year NDVI time series for Bangalore, India.
"""

import os
import sys
import time
from datetime import datetime
from typing import List, <PERSON>ple
import numpy as np
import pandas as pd
import pyarrow as pa
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from shapely.geometry import Polygon

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import terrafloww as tfw


def load_bangalore_year_data() -> tuple[pa.Table, float]:
    """
    Load full year 2024 data for Bangalore AOI and apply NDVI calculation.

    Returns:
        Tuple of (PyArrow Table with NDVI results, computation time in seconds)
    """
    print("🌍 Loading Bangalore 2024 full year data...")

    # Define Bangalore AOI
    aoi_polygon = Polygon([
        (77.55, 13.01), (77.58, 13.01),
        (77.58, 13.08), (77.55, 13.08),
        (77.55, 13.01)
    ])

    print(f"📍 AOI bounds: {aoi_polygon.bounds}")
    print(f"📅 Date range: 2024-01-01 to 2024-12-31")

    # Load data for Q1 2024 first (testing with smaller dataset)
    workflow = tfw.load(
        "sentinel-2-l2a",
        bbox=list(aoi_polygon.bounds),
        datetime="2024-01-01/2024-03-31",  # Q1 2024 for testing
        bands=["red", "nir"]
    ).apply("terrafloww.spectral.ndvi", {
        "red_band": "red",
        "nir_band": "nir"
    })

    print("🚀 Computing NDVI for Q1 2024...")
    print("⏳ This should process ~18 scenes...")

    # ⏱️ Start timing: Job submission to data receipt
    start_time = time.time()

    # Compute results (no head limit - get all available data)
    results = workflow.compute()

    # ⏱️ End timing
    computation_time = time.time() - start_time

    print(f"✅ Computation complete!")
    print(f"📊 Total spatial windows: {len(results)}")
    print(f"📋 Columns: {results.column_names}")
    print(f"⏱️ Backend processing time: {computation_time:.2f} seconds")

    return results, computation_time


def process_temporal_ndvi(results: pa.Table) -> tuple[pd.DataFrame, float]:
    """
    Process NDVI results to create temporal aggregation by date.

    Args:
        results: PyArrow table with NDVI results

    Returns:
        Tuple of (Pandas DataFrame with date and average NDVI per date, processing time in seconds)
    """
    print("📊 Processing temporal NDVI aggregation...")

    # ⏱️ Start timing: Data processing
    start_time = time.time()

    # Convert to pandas for easier processing
    df = results.to_pandas()
    
    print(f"📏 Raw data shape: {df.shape}")
    print(f"📅 Date range in data: {df['datetime'].min()} to {df['datetime'].max()}")
    
    # Extract date from datetime (remove time component)
    df['date'] = pd.to_datetime(df['datetime']).dt.date
    
    # Check if we have NDVI column or if it's in the bands array
    has_ndvi_column = 'ndvi' in df.columns
    has_ndvi_in_bands = False

    if not has_ndvi_column and 'bands' in df.columns:
        # Check if NDVI is in the bands array
        sample_bands = df['bands'].iloc[0] if len(df) > 0 else None
        if sample_bands is not None:
            has_ndvi_in_bands = 'ndvi' in sample_bands
            print(f"🔍 NDVI found in bands array: {has_ndvi_in_bands}")
            print(f"Sample bands: {sample_bands}")

    if not has_ndvi_column and not has_ndvi_in_bands:
        print("❌ No NDVI found in results")
        print(f"Available columns: {df.columns.tolist()}")
        return pd.DataFrame()

    # Extract NDVI values
    if has_ndvi_column:
        print("✅ Using NDVI from dedicated column")

        # Check if NDVI column contains arrays or scalars
        sample_ndvi = df['ndvi'].iloc[0] if len(df) > 0 else None
        print(f"🔍 Sample NDVI value: {sample_ndvi}")
        print(f"🔍 NDVI type: {type(sample_ndvi)}")

        # If NDVI contains arrays, we need to extract scalar values
        if hasattr(sample_ndvi, '__len__') and not isinstance(sample_ndvi, str):
            print("⚠️ NDVI column contains arrays, extracting mean values...")

            def extract_valid_ndvi_mean(ndvi_array):
                """Extract mean NDVI from array, filtering out invalid values."""
                if not hasattr(ndvi_array, '__len__'):
                    return float(ndvi_array)

                # Filter to valid NDVI range [-1, 1]
                valid_mask = (ndvi_array >= -1) & (ndvi_array <= 1)
                valid_ndvi = ndvi_array[valid_mask]

                if len(valid_ndvi) > 0:
                    return float(valid_ndvi.mean())
                else:
                    return np.nan  # No valid pixels

            # Extract mean NDVI value from each array, filtering outliers
            df['ndvi_scalar'] = df['ndvi'].apply(extract_valid_ndvi_mean)
            ndvi_column = 'ndvi_scalar'

            # Report filtering statistics
            total_pixels = df['ndvi'].apply(lambda x: len(x) if hasattr(x, '__len__') else 1).sum()
            valid_pixels = df['ndvi'].apply(lambda x: len(x[(x >= -1) & (x <= 1)]) if hasattr(x, '__len__') else (1 if -1 <= x <= 1 else 0)).sum()
            print(f"🔍 Filtered NDVI data: {valid_pixels}/{total_pixels} pixels valid ({valid_pixels/total_pixels*100:.1f}%)")
        else:
            print("✅ NDVI column contains scalar values")
            ndvi_column = 'ndvi'
    else:
        print("✅ Extracting NDVI from bands array")
        # For now, we'll need to extract NDVI from raster_data
        # This is more complex and might need backend changes
        print("⚠️ NDVI extraction from bands array not yet implemented")
        print("This requires extracting NDVI band from raster_data arrays")
        return pd.DataFrame()

    # Group by date and calculate statistics
    daily_stats = df.groupby('date').agg({
        ndvi_column: ['mean', 'std', 'count'],
        'chunk_id': 'count'  # Number of spatial windows per date
    }).round(4)
    
    # Flatten column names
    daily_stats.columns = ['ndvi_mean', 'ndvi_std', 'ndvi_count', 'spatial_windows']
    daily_stats = daily_stats.reset_index()
    
    # Convert date back to datetime for plotting
    daily_stats['datetime'] = pd.to_datetime(daily_stats['date'])

    # ⏱️ End timing
    processing_time = time.time() - start_time

    print(f"📈 Temporal aggregation complete:")
    print(f"  📅 Unique dates: {len(daily_stats)}")
    print(f"  📊 NDVI range: {daily_stats['ndvi_mean'].min():.3f} to {daily_stats['ndvi_mean'].max():.3f}")
    print(f"  🔢 Avg spatial windows per date: {daily_stats['spatial_windows'].mean():.1f}")
    print(f"  ⏱️ Data processing time: {processing_time:.2f} seconds")

    return daily_stats, processing_time


def analyze_temporal_patterns(daily_stats: pd.DataFrame) -> None:
    """
    Analyze temporal patterns in the NDVI time series.
    
    Args:
        daily_stats: DataFrame with daily NDVI statistics
    """
    print("🔍 Analyzing temporal patterns...")
    
    if daily_stats.empty:
        print("❌ No data to analyze")
        return
    
    # Monthly aggregation
    daily_stats['month'] = daily_stats['datetime'].dt.month
    monthly_stats = daily_stats.groupby('month').agg({
        'ndvi_mean': ['mean', 'std'],
        'spatial_windows': 'sum'
    }).round(4)
    
    print("\n📅 Monthly NDVI Summary:")
    print("Month | Avg NDVI | Std Dev | Total Windows")
    print("-" * 45)
    
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    
    for month in range(1, 13):
        if month in monthly_stats.index:
            row = monthly_stats.loc[month]
            ndvi_mean = row[('ndvi_mean', 'mean')]
            ndvi_std = row[('ndvi_mean', 'std')]
            windows = row[('spatial_windows', 'sum')]
            print(f"{month_names[month-1]:>5} | {ndvi_mean:>8.3f} | {ndvi_std:>7.3f} | {windows:>12.0f}")
        else:
            print(f"{month_names[month-1]:>5} | {'N/A':>8} | {'N/A':>7} | {'N/A':>12}")
    
    # Seasonal analysis
    print(f"\n🌱 Seasonal Analysis:")
    print(f"  🌸 Spring (Mar-May): {daily_stats[daily_stats['month'].isin([3,4,5])]['ndvi_mean'].mean():.3f}")
    print(f"  ☀️ Summer (Jun-Aug): {daily_stats[daily_stats['month'].isin([6,7,8])]['ndvi_mean'].mean():.3f}")
    print(f"  🍂 Autumn (Sep-Nov): {daily_stats[daily_stats['month'].isin([9,10,11])]['ndvi_mean'].mean():.3f}")
    print(f"  ❄️ Winter (Dec-Feb): {daily_stats[daily_stats['month'].isin([12,1,2])]['ndvi_mean'].mean():.3f}")


def create_ndvi_timeseries_plot(daily_stats: pd.DataFrame, output_file: str = "bangalore_ndvi_2024.png") -> float:
    """
    Create a beautiful NDVI time series plot.

    Args:
        daily_stats: DataFrame with daily NDVI statistics
        output_file: Output filename for the plot

    Returns:
        Visualization time in seconds
    """
    print("📊 Creating NDVI time series visualization...")

    # ⏱️ Start timing: Visualization
    start_time = time.time()

    if daily_stats.empty:
        print("❌ No data to plot")
        return 0.0

    # Set up the plot style
    plt.style.use('default')
    fig, ax = plt.subplots(figsize=(12, 8))

    # Plot the time series
    ax.plot(daily_stats['datetime'], daily_stats['ndvi_mean'],
            marker='o', linewidth=2, markersize=6,
            color='#2E8B57', alpha=0.8, label='Daily NDVI')

    # Add error bars for standard deviation
    ax.errorbar(daily_stats['datetime'], daily_stats['ndvi_mean'],
                yerr=daily_stats['ndvi_std'],
                alpha=0.3, color='#2E8B57', capsize=3)

    # Customize the plot
    ax.set_title('NDVI Time Series - Bangalore, India (2024)\n'
                'Area: 77.55°-77.58°E, 13.01°-13.08°N',
                fontsize=16, fontweight='bold', pad=20)

    ax.set_xlabel('Date', fontsize=12, fontweight='bold')
    ax.set_ylabel('NDVI (Normalized Difference Vegetation Index)', fontsize=12, fontweight='bold')

    # Format x-axis dates
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax.xaxis.set_major_locator(mdates.MonthLocator())
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # Add grid
    ax.grid(True, alpha=0.3, linestyle='--')

    # Add statistics text box
    stats_text = f"""Statistics:
    • Total observations: {len(daily_stats)}
    • NDVI range: {daily_stats['ndvi_mean'].min():.3f} to {daily_stats['ndvi_mean'].max():.3f}
    • Mean NDVI: {daily_stats['ndvi_mean'].mean():.3f}
    • Std deviation: {daily_stats['ndvi_mean'].std():.3f}"""

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
            fontsize=10, fontfamily='monospace')

    # Add legend
    ax.legend(loc='upper right')

    # Tight layout
    plt.tight_layout()

    # Save the plot
    plt.savefig(output_file, dpi=300, bbox_inches='tight')

    # ⏱️ End timing
    visualization_time = time.time() - start_time

    print(f"📊 Plot saved to: {output_file}")
    print(f"⏱️ Visualization time: {visualization_time:.2f} seconds")

    # Show the plot (optional - comment out for headless environments)
    # plt.show()

    plt.close()

    return visualization_time


def test_ndvi_timeseries_bangalore():
    """
    Main test function for Bangalore NDVI time series analysis.
    """
    print("🧪 NDVI Time Series Analysis - Bangalore 2024")
    print("=" * 60)
    
    try:
        # Step 1: Load and compute data
        print("\n📋 Step 1: Data Loading and NDVI Computation")
        results, computation_time = load_bangalore_year_data()

        if len(results) == 0:
            print("❌ No data returned from computation")
            return False

        # Step 2: Process temporal aggregation
        print("\n📋 Step 2: Temporal Processing")
        daily_stats, processing_time = process_temporal_ndvi(results)

        if daily_stats.empty:
            print("❌ Temporal processing failed")
            return False

        # Step 3: Analyze patterns
        print("\n📋 Step 3: Pattern Analysis")
        analyze_temporal_patterns(daily_stats)

        # Step 4: Create visualization
        print("\n📋 Step 4: Visualization")
        visualization_time = create_ndvi_timeseries_plot(daily_stats, "bangalore_ndvi_2024.png")

        # Step 5: Save results
        output_file = "bangalore_ndvi_2024.csv"
        daily_stats.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")

        # Step 6: Performance Summary
        total_time = computation_time + processing_time + visualization_time
        print("\n⏱️ Performance Summary:")
        print("=" * 50)
        print(f"🚀 Backend Processing:     {computation_time:>8.2f}s ({computation_time/total_time*100:>5.1f}%)")
        print(f"📊 Data Processing:       {processing_time:>8.2f}s ({processing_time/total_time*100:>5.1f}%)")
        print(f"📈 Visualization:         {visualization_time:>8.2f}s ({visualization_time/total_time*100:>5.1f}%)")
        print(f"⏱️ Total Time:            {total_time:>8.2f}s")
        print("=" * 50)

        print("\n🎉 NDVI Time Series Analysis Complete!")
        print("📊 Visualization and data export successful!")

        return True
        
    except Exception as e:
        print(f"❌ Error in NDVI time series analysis: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_ndvi_timeseries_bangalore()
    sys.exit(0 if success else 1)
