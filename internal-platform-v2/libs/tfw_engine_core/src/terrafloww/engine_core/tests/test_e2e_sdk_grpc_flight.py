# services/processing_engine/tests/test_e2e_sdk_grpc_flight.py

import pytest
import pyarrow as pa
import pyarrow.flight as flight
import numpy as np
from datetime import datetime
import pytz
import os
# import subprocess # No longer needed
import time
# import signal # No longer needed
import asyncio
import threading # Use threading
import grpc # Need grpc status codes potentially

# Make sure Ray is available but don't initialize here - let driver do it
import ray

# SDK Imports - Updated to use public SDK
try:
    # Try to import from the public SDK first
    import sys
    sys.path.insert(0, '/home/<USER>/Work/platform-build/terrafloww-sdk-public/src')
    import terrafloww as tfw
    from terrafloww.workflow import Workflow
    tfw_load = tfw.load
except ImportError as e:
    pytest.skip(f"Cannot import public terrafloww SDK: {e}. Ensure public SDK is available.", allow_module_level=True)

# Schema Import (remains the same)
try:
    from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA
except ImportError as e:
    pytest.skip(f"Cannot import tfw_raster_schemas: {e}.", allow_module_level=True)

# Import the main server function and potentially caches/locks if needed for direct manipulation
# We SHOULD NOT need direct cache access in *this* test, as the driver populates it.
# We only need the 'serve' function to run it in a thread.
try:
    from processing_engine.app.main import serve as run_backend_servers
    # If we needed direct cache access:
    # from terrafloww.engine_core.runtime_ray.driver import LOCAL_FLIGHT_RESULT_CACHE, ...
except ImportError as e:
    pytest.skip(f"Cannot import backend server 'serve' function: {e}.", allow_module_level=True)


@pytest.fixture(scope="module")
def running_backend_server():
    """
    Starts the combined gRPC & Flight servers in a background thread
    using the logic from processing_engine/app/main.py.
    """
    grpc_port = os.environ.get("GRPC_PORT", "50051")
    flight_port = os.environ.get("FLIGHT_PORT", "50052")
    print(f"\nStarting backend server thread (gRPC: {grpc_port}, Flight: {flight_port})...")

    # Use a flag or event to signal when servers are ready or stopped
    server_ready_event = threading.Event()
    stop_event = threading.Event()
    server_exception = None

    def run_server_thread():
        nonlocal server_exception
        try:
            # Run the main async serve function in a new event loop for this thread
            # This isolates the server's asyncio loop from pytest's loop.
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            # How to signal readiness? main.serve() currently blocks until termination.
            # We might need to modify main.serve() slightly or just wait.
            # Let's assume main.serve handles internal setup and blocks.
            print("[Server Thread] Starting asyncio.run(run_backend_servers)...")
            # We can't directly capture readiness easily here, so we rely on time.sleep below.
            loop.run_until_complete(run_backend_servers()) # main.serve() now runs here
            print("[Server Thread] run_backend_servers finished.")
        except Exception as e:
            server_exception = e
            print(f"[Server Thread] Exception: {e}")
        finally:
            stop_event.set() # Signal that the server loop has exited

    thread = threading.Thread(target=run_server_thread, daemon=True)
    thread.start()

    # Wait for the server to likely start up
    print("Waiting for backend server thread to initialize...")
    time.sleep(10) # Increased wait time for Ray init + servers

    if server_exception:
        pytest.fail(f"Server thread failed to start: {server_exception}")
    if not thread.is_alive():
         pytest.fail("Server thread terminated prematurely.")

    print("Backend server thread presumed running.")

    yield # Test runs here

    # Teardown: Need a way to gracefully stop the asyncio loop in the thread.
    # Modifying main.serve to watch for a stop event or using signals is complex.
    # For testing, we might rely on the process ending, but proper shutdown is better.
    # Simplest (less graceful) for now: test finishes, thread exits as daemon.
    # TODO: Implement graceful shutdown mechanism in main.serve triggered by an event.
    print("\nTest finished, server thread will exit as daemon.")


# --- Test Configuration (remains the same) ---
# ... CATALOG_PATH, TEST_INGEST_AOI_WKT, etc. ...
CATALOG_PATH = os.environ.get("TFW_EXTERNAL_CATALOG_PATH", "/tmp/platform_delta_tables/ext_stac_datasets")
TEST_INGEST_AOI_WKT = box(-74.05, 40.75, -73.95, 40.85).wkt # Matches test_dsl_compute AOI
TEST_INGEST_DATETIME = "2024-03-01T00:00:00Z/2024-03-03T23:59:59Z" # Matches test_dsl_compute
TEST_COLLECTION = "sentinel-2-l2a" # Actual collection name
TEST_BAND_RED = "red"
TEST_BAND_NIR = "nir"

@pytest.fixture(scope="module", autouse=True)
def ensure_catalog_path_sdk():
    # ... (remains the same) ...
    if not os.path.exists(os.path.join(CATALOG_PATH, "_delta_log")):
        pytest.skip(f"Test catalog not found at {CATALOG_PATH}. Run ingest_ext_stac.py first.")


@pytest.mark.asyncio
# Use the new fixture name
async def test_sdk_basic_flow(running_backend_server):
    """
    Tests the full flow: SDK -> gRPC -> Ray Execution -> Flight -> SDK Result.
    Relies on the running_backend_server fixture.
    """
    # --- 1. Define Workflow using SDK ---
    print("Defining SDK workflow...")
    head_limit = 1
    workflow = (
        tfw_load(TEST_COLLECTION)
        .filter(
            aoi=TEST_INGEST_AOI_WKT,
            aoi_crs="EPSG:4326",
            datetime_filter=TEST_INGEST_DATETIME,
            bands=[TEST_BAND_RED, TEST_BAND_NIR]
        )
        .apply("ndvi")
        .head(head_limit)
    )
    assert isinstance(workflow, GeoImageCollection)

    # --- 2. Execute Workflow via SDK ---
    print(f"Calling SDK compute() for head({head_limit})...")
    result_table = None
    try:
        start_compute = time.time()
        # IMPORTANT: Ensure GeoImageCollection.compute() is implemented!
        result_table = workflow.compute() # This triggers gRPC -> Driver -> Worker -> Cache -> Flight
        end_compute = time.time()
        print(f"SDK compute() finished in {end_compute - start_compute:.2f} seconds.")

    # More specific exceptions first
    except NotImplementedError:
         pytest.fail("SDK's GeoImageCollection.compute() method is not implemented.")
    except flight.FlightUnavailableError as e:
         # Add check for status cache if possible (needs cache import back)
         # status = LOCAL_FLIGHT_STATUS_CACHE.get(execution_id) # Need exec_id
         pytest.fail(f"Flight Unavailable - Job likely didn't complete or status update failed: {e}")
    except flight.FlightInternalError as e:
         pytest.fail(f"Flight Internal Server Error - Backend execution likely failed: {e}")
    except grpc.RpcError as e:
         # Check status code for more details
         status_code = e.code()
         pytest.fail(f"gRPC Error ({status_code}) - Failed to communicate with Processing Engine: {e.details()}")
    except Exception as e:
        pytest.fail(f"SDK compute() failed unexpectedly: {e}", pytrace=True)


    # --- 3. Assertions ---
    assert result_table is not None, "SDK compute() returned None"
    assert isinstance(result_table, pa.Table), f"SDK compute() returned type {type(result_table)}, expected pyarrow.Table"
    print(f"SDK received Table. Rows: {result_table.num_rows}, Schema:\n{result_table.schema}")

    assert result_table.num_rows == head_limit, f"Expected {head_limit} rows due to .head(), got {result_table.num_rows}"

    expected_col_names = set(RASTER_CHUNK_SCHEMA.names) | {'ndvi'}
    actual_col_names = set(result_table.schema.names)
    if actual_col_names != expected_col_names:
        print(f"DEBUG: Expected Columns: {sorted(list(expected_col_names))}")
        print(f"DEBUG: Actual Columns:   {sorted(list(actual_col_names))}")
    assert actual_col_names == expected_col_names, f"Schema mismatch in final result."

    # ... (Optional content check remains the same) ...
    if result_table.num_rows > 0:
        row = result_table.to_pylist()[0]
        assert 'ndvi' in row and isinstance(row['ndvi'], list)